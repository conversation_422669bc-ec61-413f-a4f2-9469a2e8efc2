import { Test, TestingModule } from '@nestjs/testing';
import { AdminService } from './admins.service';
import { PrismaService } from 'prisma/prisma.service';
import { AdminRole } from '@prisma/client';
import { CreateAdminDto } from 'src/shared/dtos';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AdminService', () => {
  let service: AdminService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    users: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    admins: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAdmin', () => {
    const createAdminDto: CreateAdminDto = {
      lastName: 'Doe',
      firstName: 'John',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123',
      countryId: 1,
      stateId: 1,
      accessType: AdminRole.ADMIN,
      permissions: {
        users: ['read', 'create'],
        roles: ['read'],
      },
    };

    it('should create admin successfully', async () => {
      // Mock no existing email
      mockPrismaService.users.findFirst.mockResolvedValue(null);
      mockPrismaService.admins.findFirst.mockResolvedValue(null);

      // Mock bcrypt hash
      mockedBcrypt.hash.mockResolvedValue('hashedPassword123' as never);

      // Mock transaction
      const mockUser = { id: 1, publicId: 'user-uuid' };
      const mockAdmin = {
        id: 1,
        publicId: 'admin-uuid',
        ...createAdminDto,
        userId: 1,
        user: mockUser,
        country: null
      };

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          users: {
            create: jest.fn().mockResolvedValue(mockUser),
          },
          admins: {
            create: jest.fn().mockResolvedValue(mockAdmin),
          },
        });
      });

      const result = await service.createAdmin(createAdminDto);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Admin created successfully');
      expect(result.result).toEqual(mockAdmin);
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('password123', 10);
    });

    it('should return error if email already exists', async () => {
      // Mock existing email
      mockPrismaService.users.findFirst.mockResolvedValue({ id: 1, email: createAdminDto.email });

      const result = await service.createAdmin(createAdminDto);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Un utilisateur ou administrateur avec cette adresse email existe déjà');
    });
  });
});
