// src/helpers/pagination.helper.ts

import { Prisma } from '@prisma/client';
import { PaginatedResult, PaginationMeta, PrismaModel } from '../interfaces';

export class PaginationHelper {
    static async paginate<T, WhereInput, OrderByInput, IncludeInput, SelectInput>(
        model: PrismaModel,
        params: {
            page: number;
            limit: number;
            where?: WhereInput;
            orderBy?: OrderByInput | OrderByInput[];
            include?: IncludeInput;
            select?: SelectInput;
        }
    ): Promise<PaginatedResult<T>> {
        const { page, limit, where, orderBy, include, select } = params;
        const skip = (page - 1) * limit;

        const [total, data] = await Promise.all([
            model.count({ where }),
            model.findMany({
                skip,
                take: limit,
                where,
                orderBy,
                include,
                select,
            }),
        ]);

        const total_pages = Math.ceil(total / limit);

        const meta: PaginationMeta = {
            total,
            total_pages,
            current_page: page,
            limit,
            next_page: page < total_pages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
            first_page: 1,
            last_page: total_pages,
        };

        return { meta, data: data as T[] };
    }

    static getPaginationParams(page: number = 1, limit: number = 10) {
        return {
            skip: (page - 1) * limit,
            take: limit,
        };
    }
}