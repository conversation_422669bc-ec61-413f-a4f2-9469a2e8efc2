import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';



async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();


import { MicroserviceOptions, Transport } from '@nestjs/microservices';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.NATS,
      options: {
        servers: [process.env.NATS_URL || 'nats://localhost:4222'],
        queue: 'auth_queue',
        reconnect: true,
        maxReconnectAttempts: -1,
        reconnectTimeWait: 3000,
        pingInterval: 120000,
      },
    },
  );
  await app.listen();
}
bootstrap();import { MicroserviceOptions, Transport } from '@nestjs/microservices';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.NATS,
      options: {
        servers: [process.env.NATS_URL || 'nats://localhost:4222'],
        queue: 'auth_queue',
        reconnect: true,
        maxReconnectAttempts: -1,
        reconnectTimeWait: 3000,
        pingInterval: 120000,
      },
    },
  );
  await app.listen();
}
bootstrap();