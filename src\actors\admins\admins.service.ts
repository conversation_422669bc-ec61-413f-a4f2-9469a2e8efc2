import { Injectable, NotFoundException } from '@nestjs/common';
import { AdminRole, Prisma } from '@prisma/client';
import { PrismaService } from 'prisma/prisma.service';
import { ApiResponseHelper } from 'src/shared/helpers/api-response.helper';
import { PaginationHelper } from 'src/shared/helpers/pagination.helper';
import { Admin, ApiResponse, UserStatus } from 'src/shared/interfaces';
import { AdminQueryDto, CreateAdminDto, UpdateAdminDto } from 'src/shared/dtos';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminService {
    private apiResponse = ApiResponseHelper;

    constructor(private readonly prisma: PrismaService) { }

    /**
     * Get a paginated list of all admins
     */
    async getAdmins(params: AdminQueryDto): Promise<ApiResponse> {
        try {
            // Build where conditions based on query params
            const where: Prisma.adminsWhereInput = {};

            if (params.accessType) {
                where.accessType = params.accessType;
            }

            if (params.countryId) {
                where.countryId = params.countryId;
            }

            if (params.search) {
                where.OR = [
                    { firstName: { contains: params.search } },
                    { lastName: { contains: params.search } },
                    { email: { contains: params.search } }
                ];
            }

            const result = await PaginationHelper.paginate<
                Admin,
                Prisma.adminsWhereInput,
                Prisma.adminsOrderByWithRelationInput,
                Prisma.adminsInclude,
                Prisma.adminsSelect
            >(
                this.prisma.admins,
                {
                    page: params.page || 1,
                    limit: params.limit || 10,
                    where,
                    include: {
                        user: true,
                        country: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                }
            );

            return this.apiResponse.success('Admins retrieved successfully', result);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la récupération des administrateurs",
                error
            );
        }
    }

    /**
     * Get admin details by ID
     */
    async getAdminById(id: string): Promise<ApiResponse> {
        try {
            const admin = await this.prisma.admins.findUnique({
                where: { publicId: id },
                include: {
                    user: true,
                    country: true,
                }
            });

            if (!admin) {
                return this.apiResponse.error(
                    "L'administrateur n'a pas été trouvé",
                    { message: "Admin not found" }
                );
            }

            return this.apiResponse.success('Admin details retrieved successfully', admin);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la récupération des détails de l'administrateur",
                error
            );
        }
    }

    /**
     * Create a new admin (creates both user account and admin profile)
     * Based on the logic from prisma/seeds/admins.seeder.ts
     */
    async createAdmin(createAdminDto: CreateAdminDto): Promise<ApiResponse> {
        try {
            // Check if email already exists in users or admins table
            const [userEmailExists, adminEmailExists] = await Promise.all([
                this.prisma.users.findFirst({
                    where: { email: createAdminDto.email }
                }),
                this.prisma.admins.findFirst({
                    where: { email: createAdminDto.email }
                })
            ]);

            if (userEmailExists || adminEmailExists) {
                return this.apiResponse.error(
                    "Un utilisateur ou administrateur avec cette adresse email existe déjà",
                    { message: "Email already exists" }
                );
            }

            const hashedPassword = await bcrypt.hash(createAdminDto.password, 10);

            const username = `${createAdminDto.firstName} ${createAdminDto.lastName.toUpperCase()}`;

            // Create user and admin in a transaction (following seeder logic)
            const [user, admin] = await this.prisma.$transaction(async (tx) => {
                // Create the user account first
                const user = await tx.users.create({
                    data: {
                        publicId: uuidv4(),
                        username: username,
                        email: createAdminDto.email,
                        password: hashedPassword,
                        status: UserStatus.ACTIVE,
                        countryId: createAdminDto.countryId,
                        roleId: createAdminDto.roleId || 2, 
                        activatedAt: new Date(),
                        phoneVerifiedAt: new Date(),
                        emailVerifiedAt: new Date(),
                        createdAt: new Date(),
                        updatedAt: new Date()
                    }
                });

                // Create the admin profile
                const admin = await tx.admins.create({
                    data: {
                        publicId: uuidv4(),
                        lastName: createAdminDto.lastName,
                        firstName: createAdminDto.firstName,
                        email: createAdminDto.email,
                        phone: createAdminDto.phone,
                        userId: user.id,
                        countryId: createAdminDto.countryId,
                        stateId: createAdminDto.stateId,
                        accessType: createAdminDto.accessType || AdminRole.ADMIN,
                        permissions: createAdminDto.permissions,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    },
                    include: {
                        user: true,
                        country: true,
                    }
                });

                return [user, admin];
            });

            return this.apiResponse.success('Admin created successfully', admin);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la création de l'administrateur",
                error
            );
        }
    }

    /**
     * Update an admin
     */
    async updateAdmin(id: string, updateAdminDto: UpdateAdminDto): Promise<ApiResponse> {
        try {
            // Check if admin exists
            const existingAdmin = await this.prisma.admins.findUnique({
                where: { publicId: id }
            });

            if (!existingAdmin) {
                throw new NotFoundException(`Admin with ID ${id} not found`);
            }

            // Check if email is being updated and if it already exists
            if (updateAdminDto.email && updateAdminDto.email !== existingAdmin.email) {
                const emailExists = await this.prisma.admins.findUnique({
                    where: { email: updateAdminDto.email }
                });

                if (emailExists) {
                    return this.apiResponse.error(
                        "Un administrateur avec cette adresse email existe déjà",
                        { message: "Email already exists" }
                    );
                }
            }

            // Update the admin
            const updatedAdmin = await this.prisma.admins.update({
                where: { publicId: id },
                data: {
                    ...updateAdminDto,
                    updatedAt: new Date(),
                },
                include: {
                    user: true,
                    country: true,
                }
            });

            return this.apiResponse.success('Admin updated successfully', updatedAdmin);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la mise à jour de l'administrateur",
                error
            );
        }
    }

    /**
     * Delete an admin
     */
    async deleteAdmin(id: string): Promise<ApiResponse> {
        try {
            // Check if admin exists
            const existingAdmin = await this.prisma.admins.findUnique({
                where: { publicId: id }
            });

            if (!existingAdmin) {
                throw new NotFoundException(`Admin with ID ${id} not found`);
            }

            // Delete the admin
            await this.prisma.admins.delete({
                where: { publicId: id }
            });

            return this.apiResponse.success('Admin deleted successfully', null);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la suppression de l'administrateur",
                error
            );
        }
    }
}