import { Injectable } from '@nestjs/common';
import { PrismaService } from 'prisma/prisma.service';
import { ApiResponseHelper } from 'src/shared/helpers/api-response.helper';
import { PaginationHelper } from 'src/shared/helpers/pagination.helper';
import { Admin, ApiResponse } from 'src/shared/interfaces';


@Injectable()
export class AdminService {
    constructor(private readonly prisma: PrismaService) { }

    async getAdmins(params: { page: number; limit: number }): Promise<ApiResponse> {
        try {
            const result = await PaginationHelper.paginate<Admin, any, any>(
                this.prisma.admins,
                {
                    page: params.page,
                    limit: params.limit,
                    include: {
                        user: true,
                        country: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                }
            );

            return ApiResponseHelper.success('Admins retrieved successfully', result);
        } catch (error) {
            return ApiResponseHelper.error(
                "Une erreur s'est produite lors de la récupération des administrateurs",
                error
            );
        }
    }
}