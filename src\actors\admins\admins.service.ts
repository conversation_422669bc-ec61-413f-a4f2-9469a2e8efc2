import { Injectable, NotFoundException } from '@nestjs/common';
import { AdminRole, Prisma } from '@prisma/client';
import { PrismaService } from 'prisma/prisma.service';
import { ApiResponseHelper } from 'src/shared/helpers/api-response.helper';
import { PaginationHelper } from 'src/shared/helpers/pagination.helper';
import { Admin, ApiResponse } from 'src/shared/interfaces';
import { CreateAdminDto, UpdateAdminDto } from './dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AdminService {
    private apiResponse = ApiResponseHelper;

    constructor(private readonly prisma: PrismaService) { }

    /**
     * Get a paginated list of all admins
     */
    async getAdmins(params: { page: number; limit: number }): Promise<ApiResponse> {
        try {
            const result = await PaginationHelper.paginate<
                Admin,
                Prisma.adminsWhereInput,
                Prisma.adminsOrderByWithRelationInput,
                Prisma.adminsInclude,
                Prisma.adminsSelect
            >(
                this.prisma.admins,
                {
                    page: params.page,
                    limit: params.limit,
                    include: {
                        user: true,
                        country: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                }
            );

            return this.apiResponse.success('Admins retrieved successfully', result);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la récupération des administrateurs",
                error
            );
        }
    }

    /**
     * Get admin details by ID
     */
    async getAdminById(id: string): Promise<ApiResponse> {
        try {
            const admin = await this.prisma.admins.findUnique({
                where: { publicId: id },
                include: {
                    user: true,
                    country: true,
                }
            });

            if (!admin) {
                return this.apiResponse.error(
                    "L'administrateur n'a pas été trouvé",
                    { message: "Admin not found" }
                );
            }

            return this.apiResponse.success('Admin details retrieved successfully', admin);
        } catch (error) {
            return this.apiResponse.error(
                "Une erreur s'est produite lors de la récupération des détails de l'administrateur",
                error
            );
        }
    }

    /**
     * Create a new admin
     */
    async createAdmin(createAdminDto: CreateAdminDto): Promise<ApiResponse> {
        try {
            // Check if email already exists
            const emailExists = await this.prisma.admins.findUnique({
                where: { email: createAdminDto.email }
            });

            if (emailExists) {
                return this.apiResponse.error(
                    "Un administrateur avec cette adresse email existe déjà",
                    { message: "Email already exists" }
                );
            }

            // Check if user exists
            const userExists = await this.prisma.users.findUnique({
                where: { id: createAdminDto.userId }
            });

            if (!userExists) {
                return this.apiResponse.error(
                    "L'utilisateur associé n'existe pas",
                    { message: "User not found" }
                );
            }

            // Check if user is already an admin
            const userIsAdmin = await this.prisma.admins.findUnique({
                where: { userId: createAdminDto.userId }
            });

            if (userIsAdmin) {
                return this.apiResponse.error(
                    "Cet utilisateur est déjà un administrateur",
                    { message: "User is already an admin" }
                );
            }

            // Create the admin
            const admin = await this.prisma.admins.create({
                data: {
                    publicId: uuidv4(),
                    lastName: createAdminDto.lastName,
                    firstName: createAdminDto.firstName,
                    email: createAdminDto.email,
                    phone: createAdminDto.phone,
                    userId: createAdminDto.userId,
                    countryId: createAdminDto.countryId,
                    stateId: createAdminDto.stateId,
                    accessType: createAdminDto.accessType || AdminRole.ADMIN,
                    permissions: createAdminDto.permissions,
                },
                include: {
                    user: true,
                    country: true,
                }
            });

            return ApiResponseHelper.success('Admin created successfully', admin);
        } catch (error) {
            return ApiResponseHelper.error(
                "Une erreur s'est produite lors de la création de l'administrateur",
                error
            );
        }
    }

    /**
     * Update an admin
     */
    async updateAdmin(id: string, updateAdminDto: UpdateAdminDto): Promise<ApiResponse> {
        try {
            // Check if admin exists
            const existingAdmin = await this.prisma.admins.findUnique({
                where: { publicId: id }
            });

            if (!existingAdmin) {
                throw new NotFoundException(`Admin with ID ${id} not found`);
            }

            // Check if email is being updated and if it already exists
            if (updateAdminDto.email && updateAdminDto.email !== existingAdmin.email) {
                const emailExists = await this.prisma.admins.findUnique({
                    where: { email: updateAdminDto.email }
                });

                if (emailExists) {
                    return ApiResponseHelper.error(
                        "Un administrateur avec cette adresse email existe déjà",
                        { message: "Email already exists" }
                    );
                }
            }

            // Update the admin
            const updatedAdmin = await this.prisma.admins.update({
                where: { publicId: id },
                data: {
                    ...updateAdminDto,
                    updatedAt: new Date(),
                },
                include: {
                    user: true,
                    country: true,
                }
            });

            return ApiResponseHelper.success('Admin updated successfully', updatedAdmin);
        } catch (error) {
            return ApiResponseHelper.error(
                "Une erreur s'est produite lors de la mise à jour de l'administrateur",
                error
            );
        }
    }

    /**
     * Delete an admin
     */
    async deleteAdmin(id: string): Promise<ApiResponse> {
        try {
            // Check if admin exists
            const existingAdmin = await this.prisma.admins.findUnique({
                where: { publicId: id }
            });

            if (!existingAdmin) {
                throw new NotFoundException(`Admin with ID ${id} not found`);
            }

            // Delete the admin
            await this.prisma.admins.delete({
                where: { publicId: id }
            });

            return ApiResponseHelper.success('Admin deleted successfully', null);
        } catch (error) {
            return ApiResponseHelper.error(
                "Une erreur s'est produite lors de la suppression de l'administrateur",
                error
            );
        }
    }
}