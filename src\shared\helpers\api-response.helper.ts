import { ApiResponse } from "../interfaces";

export class ApiResponseHelper {
    static success<T>(message: string, result: T): ApiResponse<T> {
        return {
            success: true,
            message,
            result,
            errors: null,
            except: null,
        };
    }

    static error(message: string, error: any): ApiResponse {
        return {
            success: false,
            message,
            result: null,
            errors: error?.message || null,
            except: error || null,
        };
    }
}