export const countries = [
    {
        name: 'Benin',
        iso: 'BJ',
        prefix: '+229'
    },
    {
        name: 'Burkina Faso',
        iso: 'BF',
        prefix: '+226'
    },
    {
        name: 'Cape Verde',
        iso: 'CP',
        prefix: '+238'
    },
    {
        name: 'Côte d\'Ivoire',
        iso: 'CI',
        prefix: '+225'
    },
    {
        name: '<PERSON><PERSON>bie',
        iso: 'GM',
        prefix: '+220'
    },
    {
        name: 'Ghana',
        iso: 'GH',
        prefix: '+233'
    },
    {
        name: '<PERSON><PERSON><PERSON>',
        iso: 'GN',
        prefix: '+224'
    },
    {
        name: 'Guinea-Bissau',
        iso: 'GNB',
        prefix: '+245'
    },
    {
        name: 'Liberia',
        iso: 'LB',
        prefix: '+231'
    },
    {
        name: 'Mali',
        iso: 'ML',
        prefix: '+223'
    },
    {
        name: 'Niger',
        iso: 'NE',
        prefix: '+227'
    },
    {
        name: 'Nigeria',
        iso: 'NG',
        prefix: '+234'
    },
    {
        name: 'Senegal',
        iso: 'SN',
        prefix: '+221'
    },
    {
        name: 'Sierra Leone',
        iso: 'SL',
        prefix: '+232'
    },
    {
        name: 'Togo',
        iso: 'TG',
        prefix: '+228'
    }
]

export const states = [
    // Benin (BJ)
    { name: 'Alibori', country_id: 1 },
    { name: 'Atacora', country_id: 1 },
    { name: 'Atlantique', country_id: 1 },
    { name: 'Borgou', country_id: 1 },
    { name: 'Collines', country_id: 1 },
    { name: 'Couffo', country_id: 1 },
    { name: 'Donga', country_id: 1 },
    { name: 'Littoral', country_id: 1 },
    { name: 'Mono', country_id: 1 },
    { name: 'Ouémé', country_id: 1 },
    { name: 'Plateau', country_id: 1 },
    { name: 'Zou', country_id: 1 },

    // Burkina Faso (BF)
    { name: 'Boucle du Mouhoun', country_id: 2 },
    { name: 'Cascades', country_id: 2 },
    { name: 'Centre', country_id: 2 },
    { name: 'Centre-Est', country_id: 2 },
    { name: 'Centre-Nord', country_id: 2 },
    { name: 'Centre-Ouest', country_id: 2 },
    { name: 'Centre-Sud', country_id: 2 },
    { name: 'Est', country_id: 2 },
    { name: 'Hauts-Bassins', country_id: 2 },
    { name: 'Nord', country_id: 2 },
    { name: 'Plateau-Central', country_id: 2 },
    { name: 'Sahel', country_id: 2 },
    { name: 'Sud-Ouest', country_id: 2 },

    // Cape Verde (CP)
    { name: 'Boa Vista', country_id: 3 },
    { name: 'Paul', country_id: 3 },
    { name: 'Porto Novo', country_id: 3 },
    { name: 'Ribeira Grande', country_id: 3 },
    { name: 'Sal', country_id: 3 },
    { name: 'São Nicolau', country_id: 3 },
    { name: 'São Vicente', country_id: 3 },
    { name: 'Brava', country_id: 3 },
    { name: 'Fogo', country_id: 3 },
    { name: 'Maio', country_id: 3 },
    { name: 'Praia', country_id: 3 },
    { name: 'Ribeira Grande de Santiago', country_id: 3 },
    { name: 'Santa Catarina', country_id: 3 },
    { name: 'Santa Catarina do Fogo', country_id: 3 },
    { name: 'Santa Cruz', country_id: 3 },
    { name: 'São Domingos', country_id: 3 },
    { name: 'São Filipe', country_id: 3 },
    { name: 'São Lourenço dos Órgãos', country_id: 3 },
    { name: 'São Miguel', country_id: 3 },
    { name: 'São Salvador do Mundo', country_id: 3 },
    { name: 'Tarrafal', country_id: 3 },
    { name: 'Tarrafal de São Nicolau', country_id: 3 },

    // Côte d'Ivoire (CI)
    { name: 'Abidjan', country_id: 4 },
    { name: 'Bas-Sassandra', country_id: 4 },
    { name: 'Comoé', country_id: 4 },
    { name: 'Denguélé', country_id: 4 },
    { name: 'Gôh-Djiboua', country_id: 4 },
    { name: 'Lacs', country_id: 4 },
    { name: 'Lagunes', country_id: 4 },
    { name: 'Montagnes', country_id: 4 },
    { name: 'Sassandra-Marahoué', country_id: 4 },
    { name: 'Savanes', country_id: 4 },
    { name: 'Vallée du Bandama', country_id: 4 },
    { name: 'Woroba', country_id: 4 },
    { name: 'Yamoussoukro', country_id: 4 },
    { name: 'Zanzan', country_id: 4 },

    // Gambia (GM)
    { name: 'Banjul', country_id: 5 },
    { name: 'Kanifing', country_id: 5 },
    { name: 'Central River', country_id: 5 },
    { name: 'Lower River', country_id: 5 },
    { name: 'North Bank', country_id: 5 },
    { name: 'Upper River', country_id: 5 },
    { name: 'West Coast', country_id: 5 },

    // Ghana (GH)
    { name: 'Ahafo', country_id: 6 },
    { name: 'Ashanti', country_id: 6 },
    { name: 'Bono', country_id: 6 },
    { name: 'Bono East', country_id: 6 },
    { name: 'Central', country_id: 6 },
    { name: 'Eastern', country_id: 6 },
    { name: 'Greater Accra', country_id: 6 },
    { name: 'North East', country_id: 6 },
    { name: 'Northern', country_id: 6 },
    { name: 'Oti', country_id: 6 },
    { name: 'Savannah', country_id: 6 },
    { name: 'Upper East', country_id: 6 },
    { name: 'Upper West', country_id: 6 },
    { name: 'Volta', country_id: 6 },
    { name: 'Western', country_id: 6 },
    { name: 'Western North', country_id: 6 },

    // Guinea (GN)
    { name: 'Boké', country_id: 7 },
    { name: 'Conakry', country_id: 7 },
    { name: 'Faranah', country_id: 7 },
    { name: 'Kankan', country_id: 7 },
    { name: 'Kindia', country_id: 7 },
    { name: 'Labé', country_id: 7 },
    { name: 'Mamou', country_id: 7 },
    { name: 'Nzérékoré', country_id: 7 },

    // Guinea-Bissau (GNB)
    { name: 'Bafatá', country_id: 8 },
    { name: 'Biombo', country_id: 8 },
    { name: 'Bissau', country_id: 8 },
    { name: 'Bolama/Bijagós', country_id: 8 },
    { name: 'Cacheu', country_id: 8 },
    { name: 'Gabú', country_id: 8 },
    { name: 'Oio', country_id: 8 },
    { name: 'Quinara', country_id: 8 },
    { name: 'Tombali', country_id: 8 },

    // Liberia (LB)
    { name: 'Bomi', country_id: 9 },
    { name: 'Bong', country_id: 9 },
    { name: 'Gbarpolu', country_id: 9 },
    { name: 'Grand Bassa', country_id: 9 },
    { name: 'Grand Cape Mount', country_id: 9 },
    { name: 'Grand Gedeh', country_id: 9 },
    { name: 'Grand Kru', country_id: 9 },
    { name: 'Lofa', country_id: 9 },
    { name: 'Margibi', country_id: 9 },
    { name: 'Maryland', country_id: 9 },
    { name: 'Montserrado', country_id: 9 },
    { name: 'Nimba', country_id: 9 },
    { name: 'River Cess', country_id: 9 },
    { name: 'River Gee', country_id: 9 },
    { name: 'Sinoe', country_id: 9 },

    // Mali (ML)
    { name: 'Bamako', country_id: 10 },
    { name: 'Gao', country_id: 10 },
    { name: 'Kayes', country_id: 10 },
    { name: 'Kidal', country_id: 10 },
    { name: 'Koulikoro', country_id: 10 },
    { name: 'Ménaka', country_id: 10 },
    { name: 'Mopti', country_id: 10 },
    { name: 'Ségou', country_id: 10 },
    { name: 'Sikasso', country_id: 10 },
    { name: 'Taoudénit', country_id: 10 },
    { name: 'Tombouctou', country_id: 10 },

    // Niger (NE)
    { name: 'Agadez', country_id: 11 },
    { name: 'Diffa', country_id: 11 },
    { name: 'Dosso', country_id: 11 },
    { name: 'Maradi', country_id: 11 },
    { name: 'Niamey', country_id: 11 },
    { name: 'Tahoua', country_id: 11 },
    { name: 'Tillabéri', country_id: 11 },
    { name: 'Zinder', country_id: 11 },

    // Nigeria (NG)
    { name: 'Abia', country_id: 12 },
    { name: 'Adamawa', country_id: 12 },
    { name: 'Akwa Ibom', country_id: 12 },
    { name: 'Anambra', country_id: 12 },
    { name: 'Bauchi', country_id: 12 },
    { name: 'Bayelsa', country_id: 12 },
    { name: 'Benue', country_id: 12 },
    { name: 'Borno', country_id: 12 },
    { name: 'Cross River', country_id: 12 },
    { name: 'Delta', country_id: 12 },
    { name: 'Ebonyi', country_id: 12 },
    { name: 'Edo', country_id: 12 },
    { name: 'Ekiti', country_id: 12 },
    { name: 'Enugu', country_id: 12 },
    { name: 'Gombe', country_id: 12 },
    { name: 'Imo', country_id: 12 },
    { name: 'Jigawa', country_id: 12 },
    { name: 'Kaduna', country_id: 12 },
    { name: 'Kano', country_id: 12 },
    { name: 'Katsina', country_id: 12 },
    { name: 'Kebbi', country_id: 12 },
    { name: 'Kogi', country_id: 12 },
    { name: 'Kwara', country_id: 12 },
    { name: 'Lagos', country_id: 12 },
    { name: 'Nasarawa', country_id: 12 },
    { name: 'Niger', country_id: 12 },
    { name: 'Ogun', country_id: 12 },
    { name: 'Ondo', country_id: 12 },
    { name: 'Osun', country_id: 12 },
    { name: 'Oyo', country_id: 12 },
    { name: 'Plateau', country_id: 12 },
    { name: 'Rivers', country_id: 12 },
    { name: 'Sokoto', country_id: 12 },
    { name: 'Taraba', country_id: 12 },
    { name: 'Yobe', country_id: 12 },
    { name: 'Zamfara', country_id: 12 },
    { name: 'Federal Capital Territory', country_id: 12 },

    // Senegal (SN)
    { name: 'Dakar', country_id: 13 },
    { name: 'Diourbel', country_id: 13 },
    { name: 'Fatick', country_id: 13 },
    { name: 'Kaffrine', country_id: 13 },
    { name: 'Kaolack', country_id: 13 },
    { name: 'Kébémer', country_id: 13 },
    { name: 'Kolda', country_id: 13 },
    { name: 'Louga', country_id: 13 },
    { name: 'Matam', country_id: 13 },
    { name: 'Saint-Louis', country_id: 13 },
    { name: 'Sédhiou', country_id: 13 },
    { name: 'Tambacounda', country_id: 13 },
    { name: 'Thiès', country_id: 13 },
    { name: 'Ziguinchor', country_id: 13 },

    // Sierra Leone (SL)
    { name: 'Eastern Province', country_id: 14 },
    { name: 'Northern Province', country_id: 14 },
    { name: 'North West Province', country_id: 14 },
    { name: 'Southern Province', country_id: 14 },
    { name: 'Western Area', country_id: 14 },

    // Togo (TG)
    { name: 'Centrale', country_id: 15 },
    { name: 'Kara', country_id: 15 },
    { name: 'Maritime', country_id: 15 },
    { name: 'Plateaux', country_id: 15 },
    { name: 'Savanes', country_id: 15 }
];

export const cities = [
    // Benin (country_id: 1)
    { name: 'Cotonou', country_id: 1, state_id: 8 }, // Littoral
    { name: 'Porto-Novo', country_id: 1, state_id: 10 }, // Ouémé
    { name: 'Parakou', country_id: 1, state_id: 4 }, // Borgou
    { name: 'Abomey', country_id: 1, state_id: 12 }, // Zou
    { name: 'Hillacondji', country_id: 1, state_id: 9 }, // Mono, border with Togo
    { name: 'Grand-Popo', country_id: 1, state_id: 9 }, // Mono, border with Togo
    { name: 'Malanville', country_id: 1, state_id: 1 }, // Alibori, border with Niger/Nigeria
    { name: 'Kandi', country_id: 1, state_id: 1 }, // Alibori, border with Burkina Faso/Niger

    // Burkina Faso (country_id: 2)
    { name: 'Ouagadougou', country_id: 2, state_id: 15 }, // Centre
    { name: 'Bobo-Dioulasso', country_id: 2, state_id: 21 }, // Hauts-Bassins
    { name: 'Koudougou', country_id: 2, state_id: 18 }, // Centre-Ouest
    { name: 'Banfora', country_id: 2, state_id: 14 }, // Cascades
    { name: 'Faramana', country_id: 2, state_id: 21 }, // Hauts-Bassins, border with Mali
    { name: 'Dakola', country_id: 2, state_id: 22 }, // Nord, border with Ghana
    { name: 'Kantchari', country_id: 2, state_id: 20 }, // Est, border with Niger/Benin
    { name: 'Orodara', country_id: 2, state_id: 21 }, // Hauts-Bassins, border with Côte d'Ivoire

    // Cape Verde (country_id: 3)
    { name: 'Praia', country_id: 3, state_id: 36 }, // Praia
    { name: 'Mindelo', country_id: 3, state_id: 32 }, // São Vicente
    { name: 'Santa Maria', country_id: 3, state_id: 30 }, // Sal
    { name: 'Espargos', country_id: 3, state_id: 30 }, // Sal
    { name: 'Assomada', country_id: 3, state_id: 38 }, // Santa Catarina
    { name: 'São Filipe', country_id: 3, state_id: 42 }, // São Filipe

    // Côte d'Ivoire (country_id: 4)
    { name: 'Abidjan', country_id: 4, state_id: 45 }, // Abidjan
    { name: 'Yamoussoukro', country_id: 4, state_id: 57 }, // Yamoussoukro
    { name: 'Bouaké', country_id: 4, state_id: 55 }, // Vallée du Bandama
    { name: 'San-Pédro', country_id: 4, state_id: 46 }, // Bas-Sassandra
    { name: 'Noé', country_id: 4, state_id: 47 }, // Comoé, border with Ghana
    { name: 'Ferkéssédougou', country_id: 4, state_id: 54 }, // Savanes, border with Burkina Faso/Mali
    { name: 'Ouangolodougou', country_id: 4, state_id: 54 }, // Savanes, border with Burkina Faso
    { name: 'Abengourou', country_id: 4, state_id: 47 }, // Comoé, border with Ghana

    // Gambia (country_id: 5)
    { name: 'Serekunda', country_id: 5, state_id: 60 }, // Kanifing
    { name: 'Banjul', country_id: 5, state_id: 59 }, // Banjul
    { name: 'Brikama', country_id: 5, state_id: 65 }, // West Coast
    { name: 'Farafenni', country_id: 5, state_id: 63 }, // North Bank, border with Senegal
    { name: 'Amadalai', country_id: 5, state_id: 63 }, // North Bank, border with Senegal
    { name: 'Basse Santa Su', country_id: 5, state_id: 64 }, // Upper River, border with Senegal

    // Ghana (country_id: 6)
    { name: 'Accra', country_id: 6, state_id: 66 }, // Greater Accra
    { name: 'Kumasi', country_id: 6, state_id: 61 }, // Ashanti
    { name: 'Tamale', country_id: 6, state_id: 68 }, // Northern
    { name: 'Takoradi', country_id: 6, state_id: 74 }, // Western
    { name: 'Aflao', country_id: 6, state_id: 73 }, // Volta, border with Togo
    { name: 'Elubo', country_id: 6, state_id: 74 }, // Western, border with Côte d'Ivoire
    { name: 'Hamile', country_id: 6, state_id: 72 }, // Upper West, border with Burkina Faso
    { name: 'Paga', country_id: 6, state_id: 71 }, // Upper East, border with Burkina Faso

    // Guinea (country_id: 7)
    { name: 'Conakry', country_id: 7, state_id: 77 }, // Conakry
    { name: 'Nzérékoré', country_id: 7, state_id: 83 }, // Nzérékoré
    { name: 'Kankan', country_id: 7, state_id: 79 }, // Kankan
    { name: 'Kindia', country_id: 7, state_id: 80 }, // Kindia
    { name: 'Guéckédou', country_id: 7, state_id: 83 }, // Nzérékoré, border with Liberia/Sierra Leone
    { name: 'Siguiri', country_id: 7, state_id: 79 }, // Kankan, border with Mali
    { name: 'Labé', country_id: 7, state_id: 81 }, // Labé, border with Senegal
    { name: 'Fria', country_id: 7, state_id: 76 }, // Boké, border with Guinea-Bissau

    // Guinea-Bissau (country_id: 8)
    { name: 'Bissau', country_id: 8, state_id: 86 }, // Bissau
    { name: 'Gabú', country_id: 8, state_id: 89 }, // Gabú
    { name: 'Bafatá', country_id: 8, state_id: 84 }, // Bafatá
    { name: 'São Domingos', country_id: 8, state_id: 88 }, // Cacheu, border with Senegal
    { name: 'Ingoré', country_id: 8, state_id: 88 }, // Cacheu, border with Senegal
    { name: 'Farim', country_id: 8, state_id: 90 }, // Oio, border with Senegal

    // Liberia (country_id: 9)
    { name: 'Monrovia', country_id: 9, state_id: 103 }, // Montserrado
    { name: 'Gbarnga', country_id: 9, state_id: 94 }, // Bong
    { name: 'Kakata', country_id: 9, state_id: 101 }, // Margibi
    { name: 'Harper', country_id: 9, state_id: 102 }, // Maryland
    { name: 'Foya', country_id: 9, state_id: 100 }, // Lofa, border with Guinea/Sierra Leone
    { name: 'Zwedru', country_id: 9, state_id: 98 }, // Grand Gedeh, border with Côte d'Ivoire
    { name: 'Voinjama', country_id: 9, state_id: 100 }, // Lofa, border with Guinea
    { name: 'Tubmanburg', country_id: 9, state_id: 93 }, // Bomi, border with Sierra Leone

    // Mali (country_id: 10)
    { name: 'Bamako', country_id: 10, state_id: 108 }, // Bamako
    { name: 'Sikasso', country_id: 10, state_id: 116 }, // Sikasso
    { name: 'Mopti', country_id: 10, state_id: 114 }, // Mopti
    { name: 'Ségou', country_id: 10, state_id: 115 }, // Ségou
    { name: 'Koutiala', country_id: 10, state_id: 116 }, // Sikasso, border with Burkina Faso/Côte d'Ivoire
    { name: 'Kayes', country_id: 10, state_id: 110 }, // Kayes, border with Senegal/Mauritania
    { name: 'Gao', country_id: 10, state_id: 109 }, // Gao, border with Niger
    { name: 'Tombouctou', country_id: 10, state_id: 118 }, // Tombouctou, border with Mauritania

    // Niger (country_id: 11)
    { name: 'Niamey', country_id: 11, state_id: 123 }, // Niamey
    { name: 'Zinder', country_id: 11, state_id: 126 }, // Zinder
    { name: 'Maradi', country_id: 11, state_id: 122 }, // Maradi
    { name: 'Agadez', country_id: 11, state_id: 119 }, // Agadez
    { name: 'Gaya', country_id: 11, state_id: 121 }, // Dosso, border with Benin/Nigeria
    { name: 'Diffa', country_id: 11, state_id: 120 }, // Diffa, border with Nigeria/Chad
    { name: 'Téra', country_id: 11, state_id: 125 }, // Tillabéri, border with Burkina Faso/Mali
    { name: 'Birni N’Konni', country_id: 11, state_id: 124 }, // Tahoua, border with Nigeria

    // Nigeria (country_id: 12)
    { name: 'Lagos', country_id: 12, state_id: 150 }, // Lagos
    { name: 'Kano', country_id: 12, state_id: 145 }, // Kano
    { name: 'Ibadan', country_id: 12, state_id: 156 }, // Oyo
    { name: 'Abuja', country_id: 12, state_id: 163 }, // Federal Capital Territory
    { name: 'Seme', country_id: 12, state_id: 150 }, // Lagos, border with Benin
    { name: 'Maiduguri', country_id: 12, state_id: 134 }, // Borno, border with Niger/Chad/Cameroon
    { name: 'Katsina', country_id: 12, state_id: 146 }, // Katsina, border with Niger
    { name: 'Calabar', country_id: 12, state_id: 135 }, // Cross River, border with Cameroon

    // Senegal (country_id: 13)
    { name: 'Dakar', country_id: 13, state_id: 164 }, // Dakar
    { name: 'Thiès', country_id: 13, state_id: 176 }, // Thiès
    { name: 'Kaolack', country_id: 13, state_id: 168 }, // Kaolack
    { name: 'Ziguinchor', country_id: 13, state_id: 177 }, // Ziguinchor
    { name: 'Saint-Louis', country_id: 13, state_id: 173 }, // Saint-Louis, border with Mauritania
    { name: 'Kidira', country_id: 13, state_id: 175 }, // Tambacounda, border with Mali
    { name: 'Matam', country_id: 13, state_id: 172 }, // Matam, border with Mauritania
    { name: 'Kolda', country_id: 13, state_id: 170 }, // Kolda, border with Guinea-Bissau

    // Sierra Leone (country_id: 14)
    { name: 'Freetown', country_id: 14, state_id: 182 }, // Western Area
    { name: 'Bo', country_id: 14, state_id: 181 }, // Southern Province
    { name: 'Kenema', country_id: 14, state_id: 178 }, // Eastern Province
    { name: 'Makeni', country_id: 14, state_id: 179 }, // Northern Province
    { name: 'Kambia', country_id: 14, state_id: 180 }, // North West Province, border with Guinea
    { name: 'Pujehun', country_id: 14, state_id: 181 }, // Southern Province, border with Liberia
    { name: 'Kailahun', country_id: 14, state_id: 178 }, // Eastern Province, border with Guinea/Liberia

    // Togo (country_id: 15)
    { name: 'Lomé', country_id: 15, state_id: 145 }, // Maritime
    { name: 'Kara', country_id: 15, state_id: 144 }, // Kara
    { name: 'Sokodé', country_id: 15, state_id: 143 }, // Centrale
    { name: 'Kpalimé', country_id: 15, state_id: 146 }, // Plateaux
    { name: 'Kojoviakope', country_id: 15, state_id: 145 }, // Maritime, border with Ghana
    { name: 'Sanvee Condji', country_id: 15, state_id: 145 }, // Maritime, border with Benin
    { name: 'Dapaong', country_id: 15, state_id: 147 }, // Savanes, border with Burkina Faso
    { name: 'Kémérida', country_id: 15, state_id: 147 } // Savanes, border with Ghana
];

export const vehicle_types = [
    {
        name: "Camion plateau"
    },
    {
        name: "Camion benne"
    },
    {
        name: "Camion frigorifique"
    },
    {
        name: "Porte-char"
    },
    {
        name: "Semi-remorque"
    },
    {
        name: "Fourgonnette"
    },
    {
        name: "Voiture particulière"
    },
    {
        name: "Moto"
    },
    {
        name: "Véhicule lourd"
    },
    {
        name: "Autobus"
    }
]

export const complaint_types = [
    {
        name: "Retard de livraison",
        description: "Dépassement significatif du délai de livraison convenu"
    },
    {
        name: "Dommage à la marchandise",
        description: "Produits endommagés lors du transport"
    },
    {
        name: "Perte partielle/totale",
        description: "Marchandise manquante à la livraison"
    },
    {
        name: "Fraude",
        description: "Fausse déclaration, surfacturation ou escroquerie"
    },
    {
        name: "Abus",
        description: "Comportement abusif du personnel (verbal, physique)"
    },
    {
        name: "Tracasserie",
        description: "Contrôles ou formalités administratives excessives"
    },
    {
        name: "Défaut d'information",
        description: "Manque de transparence sur le suivi du colis"
    },
    {
        name: "Paiement indu",
        description: "Frais supplémentaires injustifiés"
    },
    {
        name: "Non-respect contrat",
        description: "Violation des termes convenus"
    },
    {
        name: "Insalubrité",
        description: "Marchandise livrée dans des conditions insalubres"
    },
    {
        name: "Discrimination",
        description: "Traitement inéquitable basé sur l'origine, le genre, etc."
    },
    {
        name: "Détournement itinéraire",
        description: "Changement de trajet non autorisé"
    },
    {
        name: "Défaut d'assurance",
        description: "Problème dans la prise en charge des dommages"
    },
    {
        name: "Matériel inadapté",
        description: "Véhicule non conforme au type de marchandise"
    },
    {
        name: "Conduite dangereuse",
        description: "Comportement risqué du conducteur"
    },
    {
        name: "Documentation manquante",
        description: "Papiers d'expédition incomplets"
    },
    {
        name: "Problème douanier",
        description: "Retard ou blocage à la douane"
    },
    {
        name: "Service client défaillant",
        description: "Réponse inadéquate aux demandes"
    },
    {
        name: "Problème technique",
        description: "Panne ou dysfonctionnement du véhicule"
    }
]