# Architecture Modulaire de Admin-service

## Admin-service : Liste des Fonctionnalités par Module

### 1. Module `actors`

* **Rôle** : Gérer les utilisateurs, agents, customers, rôles, et admins.
* **Tables** : `users`, `agents`, `customers`, `roles`, `admins`.
* **Fonctionnalités pour les Admins** :.

  * **Agents (`agents`)** :
    * Créer un profil agent (`userId`, `agencyId`).
    * Modifier/supprimer un profil agent.
    * Lister les agents (filtres).
    * Cons Consulter les détails d’un agent.
  * **Customers (`customers`)** :
    * Créer un profil customer (`first_name`, `last_name`, `email`, `phone`, `userId`).
    * Supporter la création pour le **Trade-service** (via gRPC).
    * Modifier/supprimer un profil customer.
    * Lister les customers (filtres).
    * Consulter les détails d’un customer.
  * **Rôles (`roles`)** :
    * Créer/modifier/supprimer un rôle (`ADMIN`, `AGENT`, `CUSTOMER`).
    * Lister les rôles.
    * Consulter les détails d’un rôle.
  * **Admins (`admins`)** :
    * Créer/modifier/supprimer un profil admin (`userId`).
    * Lister les admins.
    * Consulter les détails d’un admin.
* **Intégration** :

  * Endpoints REST : `POST /actors/users`, `GET /actors/customers`.
  * gRPC : Création de customers pour le  **Trade-service** , validation avec l’ **Auth-service** .
  * NATS : Événements `user.created`, `customer.created`, `customer.invitation_sent`.
  * Cache Redis : `roles`.

### 2. Module `configurations`

* **Rôle** : Gérer les entités de configuration.
* **Tables** : `countries`, `states`, `cities`, `localities`, `vehicle_types`, `complaint_types`, `categories`, `tags`.
* **Fonctionnalités pour les Admins** :
  * Créer/modifier/supprimer des pays, états, villes, localités.
  * Créer/modifier/supprimer des types de plaintes (`complaint_types`).
  * Créer/modifier/supprimer des types de véhicules (`vehicle_types`).
  * Créer/modifier/supprimer des catégories (`categories`) pour forum/annonces.
  * Créer/modifier/supprimer des tags (`tags`) pour forum.
  * Lister et consulter les détails de chaque entité (avec filtres).
* **Intégration** :
  * Endpoints REST : `POST /configurations/countries`, `GET /configurations/vehicle_types`.
  * gRPC : Fournir les configurations au  **Trade-service** .
  * NATS : Événements `configuration.created`, `configuration.updated`.
  * Cache Redis : `complaint_types`, `vehicle_types`, `categories`, `tags`.

### 3. Module `stats`

* **Rôle** : Gérer les statistiques et exports d’analyses.
* **Tables** : `analytics_exports`.
* **Fonctionnalités pour les Admins** :
  * Générer des statistiques (plaintes, transports, forum).
  * Créer/télécharger/supprimer des exports d’analyses (`analytics_exports`).
  * Afficher des tableaux de bord (métriques).
* **Intégration** :
  * Endpoints REST : `POST /stats/exports`, `GET /stats/exports`.
  * NATS : Consommer `complaint.created`, `transport.created`.
  * Cache Redis : Statistiques fréquentes.

### 4. Module `oversight`

* **Rôle** : Superviser les plaintes et transports.
* **Tables** : `complaints`, `transports` (lecture/écriture).
* **Fonctionnalités pour les Admins** :
  * Lister/consulter les plaintes (filtres : `status`, `agentId`).
  * Mettre à jour le statut des plaintes.
  * Assigner des plaintes à un agent.
  * Lister/consulter les transports (filtres).
  * Mettre à jour les transports.
  * Marquer une plainte/transport pour escalade.
* **Intégration** :
  * Endpoints REST : `GET /oversight/complaints`, `PATCH /oversight/complaints/:id`.
  * NATS : Événements `complaint.escalated`, `transport.updated`.
  * gRPC : Valider les permissions via l’ **Auth-service** .

### 5. Module `forum`

* **Rôle** : Gérer la modération du forum.
* **Tables** : `reported_messages`, `topics`, `messages`.
* **Fonctionnalités pour les Admins** :
  * Lister/consulter les messages signalés (`reported_messages`).
  * Agir sur les signalements (supprimer/masquer/ignorer).
  * Modérer les topics (supprimer/verrouiller).
  * Modérer les messages (supprimer/modifier).
* **Intégration** :
  * Endpoints REST : `GET /forum/reported-messages`, `PATCH /forum/messages/:id`.
  * NATS : Consommer `message.reported`, publier `message.moderated`.
  * gRPC : Valider les permissions.

### 6. Module `announcements`

* **Rôle** : Gérer les annonces.
* **Tables** : `announcements`.
* **Fonctionnalités pour les Admins** :
  * Créer/modifier/supprimer une annonce (`title`, `content`, `imageUrl`, `categoryId`).
  * Lister/consulter les annonces (filtres).
* **Intégration** :
  * Endpoints REST : `POST /announcements`, `GET /announcements`.
  * gRPC : Fournir les annonces au  **Trade-service** .
  * NATS : Événements `announcement.created`, `announcement.updated`.
  * Cache Redis : `announcements`.

### 7. Module `notifications`

* **Rôle** : Gérer les notifications, y compris les emails d’invitation.
* **Tables** : `notifications`.
* **Fonctionnalités pour les Admins** :
  * Envoyer une notification (email, SMS) à un utilisateur.
  * Gérer les emails d’invitation (`customer.invitation_sent`).
  * Lister/consulter/supprimer les notifications.
  * Configurer les templates de notification.
* **Intégration** :
  * Endpoints REST : `POST /notifications`, `GET /notifications`.
  * NATS : Consommer `customer.invitation_sent`, publier `notification.sent`.

### 8. Module `organizations` (si pertinent)

* **Rôle** : Gérer les organisations et leurs responsables.
* **Tables** : `organizations`, `organization_responsibles`, `access_keys`.
* **Fonctionnalités pour les Admins** :
  * Créer/modifier/supprimer une organisation.
  * Associer/supprimer des responsables (`userId`).
  * Créer/révoquer des clés d’accès (`access_keys`).
  * Lister/consulter les organisations, responsables, clés.
  * Superviser les plaintes/transports par organisation.
* **Intégration** :
  * Endpoints REST : `POST /organizations`, `GET /organizations`.
  * NATS : Événements `organization.created`, `access_key.created`.
  * gRPC : Fournir des données au  **Trade-service** .

9. **Module users**

   	

**Utilisateurs (`users`)** :* Modifier un utilisateur.

* Supprimer un utilisateur.
* Lister les utilisateurs (filtres : `role`, `createdAt`).
* Consulter les détails d’un utilisateur.
* Réinitialiser le mot de passe (token d’invitation)

## Recommandations

* **Implémentation** : Générer les modules et configurer les endpoints REST/gRPC.
* **Sécurité** : Restreindre l’accès aux admins via l’API Gateway et gRPC.
* **Performance** : Utiliser Prisma et Redis pour le cache.
* **Tests** : Tests unitaires (Jest) et end-to-end (Cypress).
* **Déploiement** : CI/CD avec GitHub Actions et Docker.
