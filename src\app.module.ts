import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ActorsModule } from './actors/actors.module';
import { UsersModule } from './users/users.module';
import { LocationsModule } from './configs/locations/locations.module';
import { RolesModule } from './configs/roles/roles.module';
import { ConfigsModule } from './configs/configs.module';
import { ForumModule } from './forum/forum.module';
import { AnnouncementsModule } from './announcements/announcements.module';
import { TradeModule } from './trade/trade.module';
import { OrganizationsModule } from './organizations/organizations.module';
import { AnalyticsModule } from './analytics/analytics.module';

@Module({
  imports: [ActorsModule, UsersModule, LocationsModule, RolesModule, ConfigsModule, ForumModule, AnnouncementsModule, TradeModule, OrganizationsModule, AnalyticsModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
