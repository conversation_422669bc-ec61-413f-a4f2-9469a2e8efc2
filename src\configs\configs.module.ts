import { Module } from '@nestjs/common';
import { LocationsModule } from './locations/locations.module';
import { RolesModule } from './roles/roles.module';
import { VehicleTypesModule } from './vehicle-types/vehicle-types.module';
import { ComplaintTypesModule } from './complaint-types/complaint-types.module';
import { CategoriesModule } from './categories/categories.module';
import { TagsModule } from './tags/tags.module';

@Module({
  imports: [LocationsModule, RolesModule, VehicleTypesModule, ComplaintTypesModule, CategoriesModule, TagsModule]
})
export class ConfigsModule {}
