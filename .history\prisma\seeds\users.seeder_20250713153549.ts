import { PrismaClient } from '@prisma/client'
import { UserStatus } from '../../src/shared/interfaces';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';

export default async function UsersSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE users`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    // Create users
    const rootUsers = {
        username: "ROOT",
        email: "<EMAIL>",
        phone: "90121416",
        password: "rootPassword@123",
        roleId: 1,
        country_id: 15,
        status: import { UserStatus } from '../../src/shared/interfaces';
.ACTIVE,
    };
    rootUsers.password = await bcrypt.hash(rootUsers.password, 10);

    await prisma.users.create({
        data: {
            publicId: uuidv4(),
            username: rootUsers.username,
            email: rootUsers.email,
            phone: rootUsers.phone,
            password: rootUsers.password,
            roleId: rootUsers.roleId,
            countryId: rootUsers.country_id,
            status: rootUsers.status,
            activatedAt: new Date(),
            phoneVerifiedAt: new Date(),
            emailVerifiedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        }
    });
}
