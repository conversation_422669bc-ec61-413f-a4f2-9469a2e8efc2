import { ApiResponse } from "../interfaces";

export default class HelperController {
    
    public async errorResponse(message: string, except: any = null,errors: any = null): Promise<ApiResponse> {
        let apiResponse: ApiResponse = {
            success: false,
            message: message,
            result: null,
            errors: errors,
            except: except,
        }
        return apiResponse;
    }
}