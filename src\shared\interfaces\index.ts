import { Prisma } from '@prisma/client';
import { $Enums } from '@prisma/client';

export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    result: T | null;
    errors: any;
    except: any;
}

export interface PaginationMeta {
    total: number;
    total_pages: number;
    current_page: number;
    limit: number;
    next_page: number | null;
    previous_page: number | null;
    first_page: number;
    last_page: number;
}

export interface PaginatedResult<T> {
    meta: PaginationMeta;
    data: T[];
}

export type PrismaModel = {
    count: (args?: any) => Promise<number>;
    findMany: (args?: any) => Promise<any[]>;
};

export enum UserStatus {
    PENDING = 'PENDING',
    ACTIVE = 'ACTIVE',
    SUSPENDED = 'SUSPENDED',
    BLOCKED = 'BLOCKED',
    ARCHIVED = 'ARCHIVED',
}

export enum ComplaintStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    UNDER_REVIEW = 'UNDER_REVIEW',
    RESOLVED = 'RESOLVED',
    REJECTED = 'REJECTED',
    CLOSED = 'CLOSED',
    REOPENED = 'REOPENED',
    ESCALATED = 'ESCALATED',
}

export enum AdminRole {
    SUPER_ADMIN = 'SUPER_ADMIN',
    ADMIN = 'ADMIN',
    SUPPORT = 'SUPPORT',
}

export type AgentType = $Enums.AgentType;
export type Gender = $Enums.Gender;
export type CivilStatus = $Enums.CivilStatus;


export enum AgentLocalityStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    TEMPORARY = 'TEMPORARY',
}

export interface Country {
    id: number;
    publicId: string;
    name?: string | null;
    iso?: string | null;
    prefix?: string | null;
    currency?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    states?: State[];
    users?: User[];
    transports?: Transport[];
    agents?: Agent[];
    customers?: Customer[];
    localities?: Locality[];
    organizations?: Organization[];
    complaints?: Complaint[];
    admins?: Admin[];
    cities?: City[];
}

export interface State {
    id: number;
    publicId: string;
    name?: string | null;
    countryId?: number | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    country?: Country | null;
    cities?: City[];
    transports?: Transport[];
    agents?: Agent[];
    customers?: Customer[];
    localities?: Locality[];
    complaints?: Complaint[];
}

export interface City {
    id: number;
    publicId: string;
    name?: string | null;
    countryId?: number | null;
    stateId?: number | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    state?: State | null;
    country?: Country | null;
    localities?: Locality[];
}

export interface Role {
    id: number;
    publicId: string;
    name?: string | null;
    description?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    users?: User[];
}

export interface User {
    id: number;
    publicId: string;
    username: string | null;
    email?: string | null;
    phone?: string | null;
    password?: string | null;
    roleId: number | null;
    countryId: number | null;
    status?: UserStatus | null;
    fcmToken?: string | null;
    token?: string | null;
    isOnline?: boolean | null;
    refreshToken?: string | null;
    lastConnectedAt?: Date | null;
    phoneVerifiedAt?: Date | null;
    emailVerifiedAt?: Date | null;
    activatedAt?: Date | null;
    blockedAt?: Date | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    role?: Role | null;
    country?: Country | null;
    user_tokens?: UserToken[];
    sessions?: Session[];
    agents?: Agent[];
    customers?: Customer[];
    topics?: Topic[];
    messages?: Message[];
    reported_messages?: ReportedMessage[];
    notifications?: Notification[];
    admins?: Admin[];
}

export interface UserToken {
    id: number;
    publicId: string;
    userId?: number | null;
    fcmToken?: string | null;
    accessToken?: string | null;
    refreshToken?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    user?: User | null;
}

export interface Session {
    id: number;
    publicId: string;
    userId?: number | null;
    ipAddress?: string | null;
    userAgent?: string | null;
    payload?: any | null;
    lastActivity?: Date | null;
    isActive?: boolean | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    user?: User | null;
}

export interface ComplaintType {
    id: number;
    publicId: string;
    name?: string | null;
    description?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    complaints?: Complaint[];
}

export interface Complaint {
    id: number;
    publicId: string;
    transportId?: number | null;
    customerId?: number | null;
    agentId?: number | null;
    ownerType?: string | null;
    complaintTypeId?: number | null;
    label?: string | null;
    description?: string | null;
    status?: ComplaintStatus | null;
    startAt?: Date | null;
    endAt?: Date | null;
    amountPaid?: Prisma.Decimal | null;
    timeLost?: number | null;
    location?: string | null;
    countryId?: number | null;
    stateId?: number | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    transport?: Transport | null;
    customer?: Customer | null;
    agent?: Agent | null;
    complaint_type?: ComplaintType | null;
    country?: Country | null;
    state?: State | null;
    attachments?: ComplaintAttachment[];
}

export interface ComplaintAttachment {
    id: number;
    publicId: string;
    complaintId?: number | null;
    type?: string | null;
    url?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    complaint?: Complaint | null;
}

export interface Transport {
    id: number;
    publicId: string;
    chargerFirstName?: string | null;
    chargerLastName?: string | null;
    chargerPhone?: string | null;
    vehicleTypeId?: number | null;
    loadingNumber?: string | null;
    loadingPoint?: string | null;
    unloadingPoint?: string | null;
    vehiclePlateNumber?: string | null;
    drivingLicenceNumber?: string | null;
    greyCardNumber?: string | null;
    insuranceNumber?: string | null;
    productLabel?: string | null;
    productQuantity?: Prisma.Decimal | null;
    averageWeight?: Prisma.Decimal | null;
    averageUnitPrice?: Prisma.Decimal | null;
    totalProductValue?: Prisma.Decimal | null;
    exportDeclarationNumber?: string | null;
    peopleOnBoard?: number | null;
    passengers?: number | null;
    countryId?: number | null;
    stateId?: number | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    vehicle_type?: VehicleType | null;
    country?: Country | null;
    state?: State | null;
    complaints?: Complaint[];
}

export interface VehicleType {
    id: number;
    publicId: string;
    name?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    transports?: Transport[];
}

export interface Admin {
    id: number;
    publicId: string;
    lastName: string;
    firstName: string;
    email: string;
    phone?: string | null;
    userId: number;
    countryId?: number | null;
    stateId?: number | null;
    accessType: AdminRole;
    permissions: Prisma.JsonValue;
    lastLoginAt?: Date | null;
    createdAt: Date;
    updatedAt: Date;
    user?: User | null;
    country?: Country | null;
}

export interface Agent {
    id: number;
    publicId: string;
    lastName?: string | null;
    firstName?: string | null;
    email?: string | null;
    phone?: string | null;
    type: AgentType;
    code?: string | null;
    dateOfBirth?: Date | null;
    placeOfBirth?: string | null;
    nationality?: string | null;
    gender?: Gender | null;
    maritalStatus?: CivilStatus | null;
    nationalId?: string | null;
    residenceAddress?: string | null;
    userId?: number | null;
    countryId?: number | null;
    stateId?: number | null;
    status?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    user?: User | null;
    country?: Country | null;
    state?: State | null;
    complaints?: Complaint[];
    agent_localities?: AgentLocality[];
}

export interface Customer {
    id: number;
    publicId: string;
    lastName?: string | null;
    firstName?: string | null;
    email?: string | null;
    phone?: string | null;
    code?: string | null;
    dateOfBirth?: Date | null;
    placeOfBirth?: string | null;
    nationality?: string | null;
    gender?: Gender | null;
    maritalStatus?: CivilStatus | null;
    nationalId?: string | null;
    residenceAddress?: string | null;
    userId?: number | null;
    countryId?: number | null;
    stateId?: number | null;
    status?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    user?: User | null;
    country?: Country | null;
    state?: State | null;
    complaints?: Complaint[];
}

export interface Locality {
    id: number;
    publicId: string;
    code?: string | null;
    name: string;
    description?: string | null;
    cityId?: number | null;
    stateId?: number | null;
    countryId?: number | null;
    location?: Prisma.JsonValue | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    city?: City | null;
    state?: State | null;
    country?: Country | null;
    agents?: AgentLocality[];
}

export interface AgentLocality {
    id: number;
    publicId: string;
    agentId: number;
    localityId: number;
    isPrimary: boolean;
    startDate?: Date | null;
    endDate?: Date | null;
    status: AgentLocalityStatus;
    assignedBy?: number | null;
    createdAt: Date;
    updatedAt: Date;
    agent: Agent;
    locality: Locality;
}

export interface Category {
    id: number;
    publicId: string;
    name?: string | null;
    description?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    topics?: Topic[];
}

export interface Tag {
    id: number;
    publicId: string;
    name?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    topic_tags?: TopicTag[];
}

export interface Topic {
    id: number;
    publicId: string;
    authorId?: number | null;
    authorType?: string | null;
    title?: string | null;
    description?: string | null;
    content?: string | null;
    images?: Prisma.JsonValue | null;
    categoryId?: number | null;
    metadata?: Prisma.JsonValue | null;
    status?: string | null;
    likes?: number | null;
    dislikes?: number | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    author?: User | null;
    category?: Category | null;
    topic_tags?: TopicTag[];
    messages?: Message[];
}

export interface TopicTag {
    id: number;
    publicId: string;
    topicId?: number | null;
    tagId?: number | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    topic?: Topic | null;
    tag?: Tag | null;
}

export interface Message {
    id: number;
    publicId: string;
    topicId?: number | null;
    isParent?: boolean | null;
    parentId?: number | null;
    userId?: number | null;
    content?: string | null;
    reportNumber?: number | null;
    likes?: number | null;
    dislikes?: number | null;
    status?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    topic?: Topic | null;
    parent?: Message | null;
    user?: User | null;
    children?: Message[];
    reported_messages?: ReportedMessage[];
}

export interface ReportedMessage {
    id: number;
    publicId: string;
    messageId?: number | null;
    reportReason?: string | null;
    reportedByUserId?: number | null;
    status?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    message?: Message | null;
    reportedByUser?: User | null;
}

export interface Announcement {
    id: number;
    publicId: string;
    title?: string | null;
    content?: string | null;
    image?: string | null;
    status?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
}

export interface Notification {
    id: number;
    publicId: string;
    userId?: number | null;
    title?: string | null;
    content?: string | null;
    status?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    user?: User | null;
}

export interface Organization {
    id: number;
    publicId: string;
    name?: string | null;
    email?: string | null;
    phone?: string | null;
    logo?: string | null;
    websiteUrl?: string | null;
    countryId?: number | null;
    status?: string | null;
    responsables?: Prisma.JsonValue | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    country?: Country | null;
    organization_responsibles?: OrganizationResponsible[];
    access_keys?: AccessKey[];
}

export interface OrganizationResponsible {
    id: number;
    publicId: string;
    organizationId?: number | null;
    name?: string | null;
    email?: string | null;
    phone?: string | null;
    role?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    organization?: Organization | null;
}

export interface AccessKey {
    id: number;
    publicId: string;
    organizationId?: number | null;
    name?: string | null;
    description?: string | null;
    publicKey?: string | null;
    privateKey?: string | null;
    authorizations?: Prisma.JsonValue | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    organization?: Organization | null;
}

export interface AnalyticsExport {
    id: number;
    publicId: string;
    userId?: number | null;
    exportType?: string | null;
    exportFormat?: string | null;
    summary?: Prisma.JsonValue | null;
    filtersApplied?: Prisma.JsonValue | null;
    fileUrl?: string | null;
    status?: string | null;
    rowCount?: number | null;
    dataRangeStart?: Date | null;
    dataRangeEnd?: Date | null;
    metadata?: Prisma.JsonValue | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    user?: User | null;
}

export interface JwtPayload {
    id: number;
    sessionId: string;
    username: string;
    email: string;
    phone: string;
    roleId: number;
    countryId: number;
    status: string;
}