import { AdminRole } from '@prisma/client';
import { IsEmail, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

export class CreateAdminDto {
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @IsNotEmpty()
    @IsString()
    firstName: string;

    @IsNotEmpty()
    @IsEmail()
    email: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsNotEmpty()
    @IsNumber()
    userId: number;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsNumber()
    stateId?: number;

    @IsOptional()
    @IsEnum(AdminRole)
    accessType?: AdminRole;

    @IsNotEmpty()
    @IsObject()
    permissions: Record<string, string[]>;
}
