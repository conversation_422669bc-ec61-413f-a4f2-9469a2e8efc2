import { AdminRole } from '@prisma/client';
import { IsEmail, IsEnum, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

export class UpdateAdminDto {
    @IsOptional()
    @IsString()
    lastName?: string;

    @IsOptional()
    @IsString()
    firstName?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsNumber()
    stateId?: number;

    @IsOptional()
    @IsEnum(AdminRole)
    accessType?: AdminRole;

    @IsOptional()
    @IsObject()
    permissions?: Record<string, string[]>;
}
