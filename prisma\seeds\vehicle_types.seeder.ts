import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid';
import { vehicle_types } from './data';

export default async function VehicleTypesSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE vehicle_types`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    // Create vehicle types
    for (const vehicle_type of vehicle_types) {
        await prisma.vehicle_types.create({
            data: {
                publicId: uuidv4(),
                name: vehicle_type.name,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        });
    }
}
