import { PrismaClient } from '@prisma/client'
import { AdminRole, UserStatus } from '../../src/shared/interfaces';
import { cities } from './data';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';

export default async function AdminsSeeder(prisma: PrismaClient) {

    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE admins`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;

    const getCityId = (name: string) => {
        const city = cities.find(c => c.name === name);
        if (!city) {
            throw new Error(`City ${name} not found`);
        }
        return cities.indexOf(city) + 1;
    }

    const admins = [
        {
            lastName: 'Admin',
            firstName: 'Super',
            email: '<EMAIL>',
            phone: '90001122',
            countryId: 15,
            stateId: 106,
            accessType: AdminRole.SUPER_ADMIN,
            password: "*********",
            permissions: {
                users: ['create', 'read', 'update', 'delete'],
                roles: ['create', 'read', 'update', 'delete'],
                settings: ['create', 'read', 'update', 'delete']
            },
        },
        {
            lastName: 'Manager',
            firstName: 'System',
            email: '<EMAIL>',
            phone: '+**********',
            countryId: 15,
            stateId: getCityId('Cotonou'),
            accessType: AdminRole.ADMIN,
            password: "*********",
            permissions: {
                users: ['create', 'read', 'update', 'delete'],
                roles: ['create', 'read', 'update', 'delete'],
                settings: ['create', 'read', 'update']
            },
        }
    ];

    for (const admin of admins) {
        // First create the user account
        const hashedPassword = await bcrypt.hash(admin.password, 10);

        const emailExists = await prisma.users.findFirst({
            where: {
                email: admin.email,
            },
        });
        if (emailExists) {
            console.log(`Email ${admin.email} already exists`);
            continue;
        }

        const [user, adminData] = await prisma.$transaction(async (tx) => {
            const user = await tx.users.create({
                data: {
                    publicId: uuidv4(),
                    username: `${admin.firstName} ${admin.lastName.toUpperCase()}`,
                    email: admin.email,
                    password: hashedPassword,
                    status: UserStatus.ACTIVE,
                    countryId: admin.countryId,
                    roleId: 2,
                    activatedAt: new Date(),
                    phoneVerifiedAt: new Date(),
                    emailVerifiedAt: new Date(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            });

            const adminData = await tx.admins.create({
                data: {
                    publicId: uuidv4(),
                    lastName: admin.lastName,
                    firstName: admin.firstName,
                    email: admin.email,
                    phone: admin.phone,
                    userId: user.id,
                    countryId: admin.countryId,
                    stateId: admin.stateId,
                    accessType: admin.accessType,
                    permissions: admin.permissions,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            });

            return [user, adminData];
        });
    }

}

