# Admin Service API Documentation

## Overview

Le service Admin gère les profils administrateurs du système. Il suit la logique du seeder (`prisma/seeds/admins.seeder.ts`) pour créer à la fois un compte utilisateur et un profil admin associé.

## Endpoints

### 1. Créer un Admin

**POST** `/admins`

Crée un nouveau compte utilisateur et un profil admin associé en une seule transaction.

#### Body (CreateAdminDto)

```json
{
  "lastName": "Doe",
  "firstName": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "password": "securePassword123",
  "countryId": 15,
  "stateId": 106,
  "accessType": "ADMIN",
  "permissions": {
    "users": ["create", "read", "update", "delete"],
    "roles": ["create", "read", "update"],
    "settings": ["read", "update"]
  },
  "roleId": 2
}
```

#### Logique de création

1. **Vérification d'unicité** : Vérifie que l'email n'existe ni dans `users` ni dans `admins`
2. **Hachage du mot de passe** : Utilise bcrypt avec un salt de 10
3. **Génération du username** : `{firstName} {LASTNAME}` (ex: "John DOE")
4. **Transaction** : Crée l'utilisateur puis l'admin en une seule transaction
5. **Valeurs par défaut** :
   - `status`: `ACTIVE`
   - `roleId`: `2` (rôle admin)
   - `accessType`: `ADMIN`
   - Dates de vérification automatiquement définies

#### Réponse

```json
{
  "success": true,
  "message": "Admin created successfully",
  "result": {
    "id": 1,
    "publicId": "uuid-here",
    "lastName": "Doe",
    "firstName": "John",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "userId": 1,
    "countryId": 15,
    "stateId": 106,
    "accessType": "ADMIN",
    "permissions": {...},
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "user": {
      "id": 1,
      "publicId": "user-uuid",
      "username": "John DOE",
      "email": "<EMAIL>",
      "status": "ACTIVE"
    },
    "country": {...}
  },
  "errors": null,
  "except": null
}
```

### 2. Lister les Admins

**GET** `/admins?page=1&limit=10&accessType=ADMIN&countryId=15&search=john`

#### Paramètres de requête (AdminQueryDto)

- `page` (optionnel): Numéro de page (défaut: 1)
- `limit` (optionnel): Nombre d'éléments par page (défaut: 10)
- `accessType` (optionnel): Filtrer par type d'accès (`SUPER_ADMIN`, `ADMIN`, `SUPPORT`)
- `countryId` (optionnel): Filtrer par pays
- `search` (optionnel): Recherche dans firstName, lastName, email

### 3. Détails d'un Admin

**GET** `/admins/:publicId`

### 4. Mettre à jour un Admin

**PUT** `/admins/:publicId`

#### Body (UpdateAdminDto)

Tous les champs sont optionnels sauf les validations.

```json
{
  "lastName": "Smith",
  "firstName": "Jane",
  "phone": "+0987654321",
  "accessType": "SUPER_ADMIN",
  "permissions": {
    "users": ["create", "read", "update", "delete"],
    "roles": ["create", "read", "update", "delete"],
    "settings": ["create", "read", "update", "delete"]
  }
}
```

### 5. Supprimer un Admin

**DELETE** `/admins/:publicId`

## Types d'accès (AdminRole)

- `SUPER_ADMIN`: Accès complet au système
- `ADMIN`: Accès administrateur standard
- `SUPPORT`: Accès support client

## Structure des permissions

```json
{
  "users": ["create", "read", "update", "delete"],
  "roles": ["create", "read", "update", "delete"],
  "settings": ["create", "read", "update", "delete"],
  "reports": ["read", "export"],
  "analytics": ["read"]
}
```

## Gestion d'erreurs

Toutes les réponses d'erreur suivent le format :

```json
{
  "success": false,
  "message": "Message d'erreur en français",
  "result": null,
  "errors": "Détails de l'erreur",
  "except": "Exception complète"
}
```

## Notes importantes

1. **Sécurité** : Les mots de passe sont automatiquement hachés avec bcrypt
2. **Transaction** : La création d'admin utilise une transaction pour garantir la cohérence
3. **Validation** : Tous les DTOs utilisent class-validator pour la validation
4. **Relations** : Les réponses incluent automatiquement les relations `user` et `country`
