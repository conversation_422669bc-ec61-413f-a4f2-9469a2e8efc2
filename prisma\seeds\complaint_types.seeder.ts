import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid';
import { complaint_types } from './data';

export default async function ComplaintTypesSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE complaint_types`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;

    for (const complaint_type of complaint_types) {
        await prisma.complaint_types.create({
            data: {
                publicId: uuidv4(),
                name: complaint_type.name,
                description: complaint_type.description,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        });
    }
}
