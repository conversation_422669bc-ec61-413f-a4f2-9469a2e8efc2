import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid';

export default async function RolesSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE roles`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;

    const rolesData = [
        {
            name: "ROOT",
            description: "Super administrateur avec un accès illimité à toutes les fonctionnalités du système"
        },
        {
            name: "ADMINISTRATEUR",
            description: "Gère la configuration de la plateforme et les permissions des utilisateurs"
        },
        {
            name: "POINT FOCAL",
            description: "Coordinateur régional responsable des workflows de validation"
        },
        {
            name: "POSEUR MACARON",
            description: "Personnel autorisé pour le déploiement physique des macarons"
        },
        {
            name: "<PERSON><PERSON><PERSON> REGIONAL",
            description: "Représentant d'un siège régional"
        },
        {
            name: "ORGANISATION",
            description: "Organisation externe avec des droits d'accès limités"
        },
        {
            name: "CUSTOMER",
            description: "Client final avec des droits d'accès limités"
        }
    ];

    for (const role of rolesData) {
        await prisma.roles.create({
            data: {
                publicId: uuidv4(),
                name: role.name,
                description: role.description,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        });
    }
}