import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid';
import { states } from './data';

export default async function StatesSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE states`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    // Create states
    for (const state of states) {
        await prisma.states.create({
            data: {
                publicId: uuidv4(),
                name: state.name,
                countryId: state.country_id,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        });
    }
}
