import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid';
import { cities } from './data';

export default async function CitiesSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE cities`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    // Create cities
    for (const city of cities) {
        await prisma.cities.create({
            data: {
                publicId: uuidv4(),
                name: city.name,
                stateId: city.state_id,
                countryId: city.country_id,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        });
    }
}
