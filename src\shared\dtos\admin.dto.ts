import { AdminRole } from '@prisma/client';
import { IsEmail, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

/**
 * Base Admin DTO with common properties
 */
export class BaseAdminDto {
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @IsNotEmpty()
    @IsString()
    firstName: string;

    @IsNotEmpty()
    @IsEmail()
    email: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsNumber()
    stateId?: number;

    @IsOptional()
    @IsEnum(AdminRole)
    accessType?: AdminRole;

    @IsNotEmpty()
    @IsObject()
    permissions: Record<string, string[]>;
}

/**
 * DTO for creating a new admin
 * This will create both a user account and an admin profile
 */
export class CreateAdminDto extends BaseAdminDto {
    @IsNotEmpty()
    @IsString()
    password: string;

    @IsOptional()
    @IsNumber()
    roleId?: number; // Default to admin role if not specified
}

/**
 * DTO for updating an admin (all fields optional except validations)
 */
export class UpdateAdminDto {
    @IsOptional()
    @IsString()
    lastName?: string;

    @IsOptional()
    @IsString()
    firstName?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsNumber()
    stateId?: number;

    @IsOptional()
    @IsEnum(AdminRole)
    accessType?: AdminRole;

    @IsOptional()
    @IsObject()
    permissions?: Record<string, string[]>;
}

/**
 * DTO for admin response/output
 */
export class AdminResponseDto {
    id: number;
    publicId: string;
    lastName: string;
    firstName: string;
    email: string;
    phone?: string;
    userId: number;
    countryId?: number;
    stateId?: number;
    accessType: AdminRole;
    permissions: Record<string, any>;
    lastLoginAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    user?: any;
    country?: any;
}

/**
 * DTO for admin list queries with filters
 */
export class AdminQueryDto {
    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 10;

    @IsOptional()
    @IsEnum(AdminRole)
    accessType?: AdminRole;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsString()
    search?: string; // For searching by name or email
}
