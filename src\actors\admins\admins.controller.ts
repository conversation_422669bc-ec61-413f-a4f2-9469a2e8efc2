import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    ValidationPipe
} from '@nestjs/common';
import { AdminService } from './admins.service';
import { CreateAdminDto, UpdateAdminDto } from './dto';
import { ApiResponse } from 'src/shared/interfaces';

@Controller('admins')
export class AdminsController {
    constructor(private readonly adminService: AdminService) {}

    /**
     * Get paginated list of admins
     */
    @Get()
    async getAdmins(
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10
    ): Promise<ApiResponse> {
        return this.adminService.getAdmins({ page: Number(page), limit: Number(limit) });
    }

    /**
     * Get admin details by ID
     */
    @Get(':id')
    async getAdminById(@Param('id') id: string): Promise<ApiResponse> {
        return this.adminService.getAdminById(id);
    }

    /**
     * Create a new admin
     */
    @Post()
    async createAdmin(
        @Body(ValidationPipe) createAdminDto: CreateAdminDto
    ): Promise<ApiResponse> {
        return this.adminService.createAdmin(createAdminDto);
    }

    /**
     * Update an admin
     */
    @Put(':id')
    async updateAdmin(
        @Param('id') id: string,
        @Body(ValidationPipe) updateAdminDto: UpdateAdminDto
    ): Promise<ApiResponse> {
        return this.adminService.updateAdmin(id, updateAdminDto);
    }

    /**
     * Delete an admin
     */
    @Delete(':id')
    async deleteAdmin(@Param('id') id: string): Promise<ApiResponse> {
        return this.adminService.deleteAdmin(id);
    }
}
