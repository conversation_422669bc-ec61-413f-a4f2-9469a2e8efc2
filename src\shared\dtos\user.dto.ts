import { UserStatus } from '@prisma/client';
import { IsEmail, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsBoolean } from 'class-validator';

/**
 * Base User DTO with common properties
 */
export class BaseUserDto {
    @IsOptional()
    @IsString()
    username?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsNumber()
    roleId?: number;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsEnum(UserStatus)
    status?: UserStatus;

    @IsOptional()
    @IsString()
    fcmToken?: string;

    @IsOptional()
    @IsBoolean()
    isOnline?: boolean;
}

/**
 * DTO for creating a new user
 */
export class CreateUserDto extends BaseUserDto {
    @IsNotEmpty()
    @IsString()
    password: string;

    // Override to make required fields non-optional
    @IsNotEmpty()
    @IsString()
    username: string;

    @IsNotEmpty()
    @IsEmail()
    email: string;
}

/**
 * DTO for updating a user (all fields optional)
 */
export class UpdateUserDto {
    @IsOptional()
    @IsString()
    username?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsNumber()
    roleId?: number;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsEnum(UserStatus)
    status?: UserStatus;

    @IsOptional()
    @IsString()
    fcmToken?: string;

    @IsOptional()
    @IsBoolean()
    isOnline?: boolean;
}

/**
 * DTO for user response/output
 */
export class UserResponseDto {
    id: number;
    publicId: string;
    username?: string;
    email?: string;
    phone?: string;
    roleId?: number;
    countryId?: number;
    status?: UserStatus;
    isOnline?: boolean;
    lastConnectedAt?: Date;
    phoneVerifiedAt?: Date;
    emailVerifiedAt?: Date;
    activatedAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    role?: any;
    country?: any;
}

/**
 * DTO for user list queries with filters
 */
export class UserQueryDto {
    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 10;

    @IsOptional()
    @IsEnum(UserStatus)
    status?: UserStatus;

    @IsOptional()
    @IsNumber()
    roleId?: number;

    @IsOptional()
    @IsNumber()
    countryId?: number;

    @IsOptional()
    @IsString()
    search?: string; // For searching by username, email, or phone
}
