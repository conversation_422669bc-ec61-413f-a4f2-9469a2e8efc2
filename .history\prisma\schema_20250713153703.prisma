// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model countries {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  name      String?
  iso       String?
  prefix    String?
  currency  String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  states        states[]
  users         users[]
  transports    transports[]
  agents        agents[]
  customers     customers[]
  localities    localities[]
  organizations organizations[]
  complaints    complaints[]

  admins admins[]

  cities cities[]
}

model states {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  name      String?
  countryId Int?      @map("country_id")
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  country    countries?   @relation(fields: [countryId], references: [id])
  cities     cities[]
  transports transports[]
  agents     agents[]
  customers  customers[]
  localities localities[]
  complaints complaints[]
}

model cities {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  name      String?
  countryId Int?      @map("country_id")
  stateId   Int?      @map("state_id")
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  state   states?    @relation(fields: [stateId], references: [id])
  country countries? @relation(fields: [countryId], references: [id])

  localities localities[]
}

model roles {
  id          Int       @id @default(autoincrement())
  publicId    String    @unique @default(uuid()) @map("public_id")
  name        String?
  description String?
  createdAt   DateTime? @map("created_at")
  updatedAt   DateTime? @map("updated_at")

  users users[]
}

enum userStatus {
  PENDING // Initial state when user is created
  ACTIVE // User is verified and can use the system
  SUSPENDED // Temporary restriction
  BLOCKED // Permanent restriction
  ARCHIVED // User account archived/deleted
}

model users {
  id              Int         @id @default(autoincrement())
  publicId        String      @unique @default(uuid()) @map("public_id")
  username        String?
  email           String?
  phone           String?
  password        String?
  roleId          Int?        @map("role_id")
  countryId       Int?        @map("country_id")
  status          userStatus? @default(PENDING)
  fcmToken        String?     @map("fcm_token")
  token           String?
  isOnline        Boolean?    @map("is_online")
  refreshToken    String?     @map("refresh_token")
  lastConnectedAt DateTime?   @map("last_connected_at")
  phoneVerifiedAt DateTime?   @map("phone_verified_at")
  emailVerifiedAt DateTime?   @map("email_verified_at")
  activatedAt     DateTime?   @map("activated_at")
  blockedAt       DateTime?   @map("blocked_at")
  createdAt       DateTime?   @map("created_at")
  updatedAt       DateTime?   @map("updated_at")

  role              roles?              @relation(fields: [roleId], references: [id])
  country           countries?          @relation(fields: [countryId], references: [id])
  user_tokens       user_tokens[]
  sessions          sessions[]
  agents            agents[]
  customers         customers[]
  topics            topics[]
  messages          messages[]
  reported_messages reported_messages[]
  notifications     notifications[]
  analytics_exports analytics_exports[]

  admins admins[]

  statusChanges complaint_status_history[] @relation("UserStatusChanges")
}

model user_tokens {
  id           Int       @id @default(autoincrement())
  publicId     String    @unique @default(uuid()) @map("public_id")
  userId       Int?      @map("user_id")
  fcmToken     String?   @map("fcm_token")
  accessToken  String?   @map("access_token")
  refreshToken String?   @map("refresh_token")
  createdAt    DateTime? @map("created_at")
  updatedAt    DateTime? @map("updated_at")

  user users? @relation(fields: [userId], references: [id])
}

model sessions {
  id           Int       @id @default(autoincrement())
  publicId     String    @unique @default(uuid()) @map("public_id")
  userId       Int?      @map("user_id")
  ipAddress    String?   @map("ip_address")
  userAgent    String?   @map("user_agent")
  payload      Json?
  loginAt      DateTime  @default(now()) @map("login_at")
  logoutAt     DateTime? @map("logout_at")
  lastActivity DateTime  @map("last_activity")
  isActive     Boolean   @default(true) @map("is_active")
  createdAt    DateTime? @map("created_at")
  updatedAt    DateTime? @map("updated_at")

  user users? @relation(fields: [userId], references: [id])
}

model complaint_types {
  id          Int       @id @default(autoincrement())
  publicId    String    @unique @default(uuid()) @map("public_id")
  name        String?
  description String?
  createdAt   DateTime? @map("created_at")
  updatedAt   DateTime? @map("updated_at")

  complaints complaints[]
}

enum complaint_status {
  PENDING // Initial state when complaint is created
  IN_PROGRESS // Complaint is being processed/investigated
  UNDER_REVIEW // Complaint is being reviewed by supervisor
  RESOLVED // Complaint has been resolved successfully
  REJECTED // Complaint was found to be invalid/rejected
  CLOSED // Complaint process completed without resolution
  REOPENED // Previously resolved/closed complaint reopened
  ESCALATED // Complaint escalated to higher authority
}

model complaints {
  id              Int               @id @default(autoincrement())
  publicId        String            @unique @default(uuid()) @map("public_id")
  transportId     Int?              @map("transport_id")
  customerId      Int?              @map("customer_id")
  agentId         Int?              @map("agent_id")
  ownerType       String?           @map("owner_type")
  complaintTypeId Int?              @map("complaint_type_id")
  label           String?
  description     String?
  status          complaint_status? @default(PENDING)
  startAt         DateTime?         @map("start_at")
  endAt           DateTime?         @map("end_at")
  amountPaid      Decimal?          @map("amount_paid")
  timeLost        Int?
  location        String?
  countryId       Int?              @map("country_id")
  stateId         Int?              @map("state_id")
  createdAt       DateTime?         @map("created_at")
  updatedAt       DateTime?         @map("updated_at")

  transport      transports?             @relation(fields: [transportId], references: [id])
  customer       customers?              @relation(fields: [customerId], references: [id])
  agent          agents?                 @relation(fields: [agentId], references: [id])
  complaint_type complaint_types?        @relation(fields: [complaintTypeId], references: [id])
  country        countries?              @relation(fields: [countryId], references: [id])
  state          states?                 @relation(fields: [stateId], references: [id])
  attachments    complaint_attachments[]

  statusHistory  complaint_status_history[]
}

model complaint_attachments {
  id          Int       @id @default(autoincrement())
  publicId    String    @unique @default(uuid()) @map("public_id")
  complaintId Int?      @map("complaint_id")
  type        String?
  url         String?
  createdAt   DateTime? @map("created_at")
  updatedAt   DateTime? @map("updated_at")

  complaint complaints? @relation(fields: [complaintId], references: [id])
}

model transports {
  id               Int     @id @default(autoincrement())
  publicId         String  @unique @default(uuid()) @map("public_id")
  chargerFirstName String? @map("charger_first_name")
  chargerLastName  String? @map("charger_last_name")
  chargerPhone     String? @map("charger_phone")
  vehicleTypeId    Int?    @map("vehicle_type_id")
  loadingNumber    String? @map("loading_number")
  loadingPoint     String? @map("loading_point")
  unloadingPoint   String? @map("unloading_point")

  vehiclePlateNumber      String?   @map("vehicle_plate_number")
  drivingLicenceNumber    String?   @map("driving_licence_number")
  greyCardNumber          String?   @map("grey_card_number")
  insuranceNumber         String?   @map("insurance_number")
  productLabel            String?   @map("product_label")
  productQuantity         Decimal?  @map("product_quantity")
  averageWeight           Decimal?  @map("average_weight")
  averageUnitPrice        Decimal?  @map("average_unit_price")
  totalProductValue       Decimal?  @map("total_product_value")
  exportDeclarationNumber String?   @map("export_declaration_number")
  peopleOnBoard           Int?
  passengers              Json?
  files                   Json?    @map("files")
  customerId              Int?      @map("customer_id")
  countryId               Int?      @map("country_id")
  stateId                 Int?      @map("state_id")
  createdAt               DateTime? @map("created_at")
  updatedAt               DateTime? @map("updated_at")

  vehicle_type vehicle_types? @relation(fields: [vehicleTypeId], references: [id])
  country      countries?     @relation(fields: [countryId], references: [id])
  state        states?        @relation(fields: [stateId], references: [id])
  customer     customers?     @relation(fields: [customerId], references: [id])
  complaints   complaints[]
}

model vehicle_types {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  name      String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  transports transports[]
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  SUPPORT
}

model admins {
  id          Int       @id @default(autoincrement())
  publicId    String    @unique @default(uuid()) @map("public_id")
  lastName    String    @map("last_name")
  firstName   String    @map("first_name")
  email       String    @unique
  phone       String?
  userId      Int       @unique @map("user_id")
  countryId   Int?      @map("country_id")
  stateId     Int?      @map("state_id")
  accessType  AdminRole @default(ADMIN) @map("access_type")
  permissions Json      @map("permissions")
  lastLoginAt DateTime? @map("last_login_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  user    users?     @relation(fields: [userId], references: [id])
  country countries? @relation(fields: [countryId], references: [id])
}

enum Gender {
  MALE
  FEMALE
}

enum CivilStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
}

model agents {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  lastName  String?   @map("last_name")
  firstName String?   @map("first_name")
  email     String?
  phone     String?
  type      AgentType @default(FOCAL_POINT) @map("type")
  code      String?

  // Civil status information
  dateOfBirth      DateTime?    @map("date_of_birth")
  placeOfBirth     String?      @map("place_of_birth")
  nationality      String?
  gender           Gender?
  maritalStatus    CivilStatus? @default(SINGLE) @map("marital_status")
  nationalId       String?      @map("national_id") // National ID card number
  residenceAddress String?      @map("residence_address")

  userId    Int?      @map("user_id")
  countryId Int?      @map("country_id")
  stateId   Int?      @map("state_id")
  status    String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  user       users?       @relation(fields: [userId], references: [id])
  country    countries?   @relation(fields: [countryId], references: [id])
  state      states?      @relation(fields: [stateId], references: [id])
  complaints complaints[]

  agent_localities agent_localities[]
}

enum AgentType {
  FOCAL_POINT
  BADGE_OPERATOR
}

model customers {
  id               Int          @id @default(autoincrement())
  publicId         String       @unique @default(uuid()) @map("public_id")
  lastName         String?      @map("last_name")
  firstName        String?      @map("first_name")
  email            String?
  phone            String?
  code             String?
  // Civil status information
  dateOfBirth      DateTime?    @map("date_of_birth")
  placeOfBirth     String?      @map("place_of_birth")
  nationality      String?
  gender           Gender?
  maritalStatus    CivilStatus? @default(SINGLE) @map("marital_status")
  nationalId       String?      @map("national_id") // National ID card number
  residenceAddress String?      @map("residence_address")
  userId           Int?         @map("user_id")
  countryId        Int?         @map("country_id")
  stateId          Int?         @map("state_id")
  status           String?
  createdAt        DateTime?    @map("created_at")
  updatedAt        DateTime?    @map("updated_at")

  user       users?       @relation(fields: [userId], references: [id])
  country    countries?   @relation(fields: [countryId], references: [id])
  state      states?      @relation(fields: [stateId], references: [id])
  complaints complaints[]

  transports transports[]
}

model localities {
  id          Int     @id @default(autoincrement())
  publicId    String  @unique @default(uuid()) @map("public_id")
  code        String? @unique
  name        String
  description String?
  cityId      Int?    @map("city_id")
  stateId     Int?    @map("state_id")
  countryId   Int?    @map("country_id")

  // Champs géographiques
  location Json? // Stores latitude and longitude coordinates

  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  // Relations PRINCIPALES
  city   cities?            @relation(fields: [cityId], references: [id])
  agents agent_localities[]

  // Relations DERIVÉES (calculées au besoin)
  state   states?    @relation(fields: [stateId], references: [id])
  country countries? @relation(fields: [countryId], references: [id])

  @@index([cityId, stateId, countryId])
}

enum AgentLocalityStatus {
  ACTIVE
  INACTIVE
  TEMPORARY
}

model agent_localities {
  id         Int    @id @default(autoincrement())
  publicId   String @unique @default(uuid()) @map("public_id")
  agentId    Int    @map("agent_id")
  localityId Int    @map("locality_id")

  // Métadonnées d'affectation
  isPrimary Boolean             @default(false)
  startDate DateTime?           @map("start_date")
  endDate   DateTime?           @map("end_date")
  status    AgentLocalityStatus @default(ACTIVE)

  // Audit
  assignedBy Int?     @map("assigned_by") 
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  agent    agents     @relation(fields: [agentId], references: [id])
  locality localities @relation(fields: [localityId], references: [id])

  // Contraintes
  @@unique([agentId, localityId])
  @@index([agentId])
  @@index([localityId])
  @@index([status])
}

model categories {
  id          Int       @id @default(autoincrement())
  publicId    String    @unique @default(uuid()) @map("public_id")
  name        String?
  description String?
  createdAt   DateTime? @map("created_at")
  updatedAt   DateTime? @map("updated_at")

  topics topics[]
}

model tags {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  name      String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  topic_tags topic_tags[]
}

model topics {
  id          Int       @id @default(autoincrement())
  publicId    String    @unique @default(uuid()) @map("public_id")
  authorId    Int?      @map("author_id")
  authorType  String?   @map("author_type")
  title       String?
  description String?
  content     String?
  images      Json?
  categoryId  Int?      @map("category_id")
  metadata    Json?
  status      String?
  likes       Int?
  dislikes    Int?
  createdAt   DateTime? @map("created_at")
  updatedAt   DateTime? @map("updated_at")

  author     users?       @relation(fields: [authorId], references: [id])
  category   categories?  @relation(fields: [categoryId], references: [id])
  topic_tags topic_tags[]
  messages   messages[]
}

model topic_tags {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  topicId   Int?      @map("topic_id")
  tagId     Int?      @map("tag_id")
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  topic topics? @relation(fields: [topicId], references: [id])
  tag   tags?   @relation(fields: [tagId], references: [id])
}

model messages {
  id           Int       @id @default(autoincrement())
  publicId     String    @unique @default(uuid()) @map("public_id")
  topicId      Int?      @map("topic_id")
  isParent     Boolean?  @map("is_parent")
  parentId     Int?      @map("parent_id")
  userId       Int?      @map("user_id")
  content      String?
  reportNumber Int?      @map("report_number")
  likes        Int?
  dislikes     Int?
  status       String?
  createdAt    DateTime? @map("created_at")
  updatedAt    DateTime? @map("updated_at")

  topic             topics?             @relation(fields: [topicId], references: [id])
  parent            messages?           @relation("MessageToParent", fields: [parentId], references: [id])
  user              users?              @relation(fields: [userId], references: [id])
  children          messages[]          @relation("MessageToParent")
  reported_messages reported_messages[]
}

model reported_messages {
  id               Int       @id @default(autoincrement())
  publicId         String    @unique @default(uuid()) @map("public_id")
  messageId        Int?      @map("message_id")
  reportReason     String?   @map("report_reason")
  reportedByUserId Int?      @map("reported_by_user_id")
  status           String?
  createdAt        DateTime? @map("created_at")
  updatedAt        DateTime? @map("updated_at")

  message        messages? @relation(fields: [messageId], references: [id])
  reportedByUser users?    @relation(fields: [reportedByUserId], references: [id])
}

model announcements {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  title     String?
  content   String?
  image     String?
  status    String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")
}

model notifications {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  userId    Int?      @map("user_id")
  title     String?
  content   String?
  status    String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")

  user users? @relation(fields: [userId], references: [id])
}

model organizations {
  id           Int       @id @default(autoincrement())
  publicId     String    @unique @default(uuid()) @map("public_id")
  name         String?
  email        String?
  phone        String?
  logo         String?
  websiteUrl   String?   @map("website_url")
  countryId    Int?      @map("country_id")
  status       String?
  responsables Json?
  createdAt    DateTime? @map("created_at")
  updatedAt    DateTime? @map("updated_at")

  country                   countries?                  @relation(fields: [countryId], references: [id])
  organization_responsibles organization_responsibles[]
  access_keys               access_keys[]
}

model organization_responsibles {
  id             Int       @id @default(autoincrement())
  publicId       String    @unique @default(uuid()) @map("public_id")
  organizationId Int?      @map("organization_id")
  name           String?
  email          String?
  phone          String?
  role           String?
  createdAt      DateTime? @map("created_at")
  updatedAt      DateTime? @map("updated_at")

  organization organizations? @relation(fields: [organizationId], references: [id])
}

model access_keys {
  id             Int       @id @default(autoincrement())
  publicId       String    @unique @default(uuid()) @map("public_id")
  organizationId Int?      @map("organization_id")
  name           String?
  description    String?
  publicKey      String?   @map("public_key")
  privateKey     String?   @map("private_key")
  authorizations Json?
  createdAt      DateTime? @map("created_at")
  updatedAt      DateTime? @map("updated_at")

  organization organizations? @relation(fields: [organizationId], references: [id])
}

model analytics_exports {
  id             Int       @id @default(autoincrement())
  publicId       String    @unique @default(uuid()) @map("public_id")
  userId         Int?      @map("user_id")
  exportType     String?   @map("export_type")
  exportFormat   String?   @map("export_format")
  summary        Json?
  filtersApplied Json?     @map("filters_applied")
  fileUrl        String?   @map("file_url")
  status         String?
  rowCount       Int?      @map("row_count")
  dataRangeStart DateTime? @map("data_range_start")
  dataRangeEnd   DateTime? @map("data_range_end")
  metadata       Json?
  createdAt      DateTime? @map("created_at")
  updatedAt      DateTime? @map("updated_at")

  user users? @relation(fields: [userId], references: [id])
}

model complaint_status_history {
  id           Int               @id @default(autoincrement())
  publicId     String    @unique @default(uuid()) @map("public_id")

  complaintId  Int
  oldStatus    complaint_status?
  newStatus    complaint_status
  changedById  Int?
  changedAt    DateTime          @default(now())
  motif        String?

  complaint    complaints        @relation(fields: [complaintId], references: [id])
  changedBy    users?            @relation("UserStatusChanges", fields: [changedById], references: [id])
}

model tutorials {
  id        Int       @id @default(autoincrement())
  publicId  String    @unique @default(uuid()) @map("public_id")
  title     String?
  description String?
  videoUrl   String?
  createdAt DateTime? @map("created_at")
  updatedAt DateTime? @map("updated_at")
}