import { PrismaClient } from '@prisma/client'
import { countries, states } from './data'
import { v4 as uuidv4 } from 'uuid'
import bcrypt from 'bcrypt';

export default async function AgentSeeder(prisma: PrismaClient) {

    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE agents`;
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    // Hacher le mot de passe commun
    const hashedPassword = await bcrypt.hash('Passer@123', 10)

    // Parcourir tous les pays
    for (const country of countries) {
        // Trouver un état/region du pays actuel
        const countryStates = states.filter(s => s.country_id === countries.indexOf(country) + 1)
        if (countryStates.length === 0) continue

        const randomState = countryStates[Math.floor(Math.random() * countryStates.length)]

        // Données de l'agent
        const agentData = {
            lastName: `Agent-${country.iso}`,
            firstName: `Principal`,
            email: `agent.${country.iso.toLowerCase()}@ofr-trade.com`,
            phone: `${Math.floor(10000000 + Math.random() * (99999999 - 10000000 + 1))}`,
            type: Math.random() > 0.5 ? 'FOCAL_POINT' : 'BADGE_OPERATOR',
            code: `AG-${country.iso}-001`,
            countryId: countries.indexOf(country) + 1,
            stateId: states.indexOf(randomState) + 1
        }

        // Création transactionnelle
        await prisma.$transaction(async (tx) => {
            // 1. Créer le user d'abord
            const user = await tx.users.create({
                data: {
                    publicId: uuidv4(),
                    username: `${agentData.firstName} ${agentData.lastName}`,
                    email: agentData.email,
                    phone: agentData.phone,
                    password: hashedPassword,
                    status: 'ACTIVE',
                    countryId: agentData.countryId,
                    roleId: agentData.type === 'FOCAL_POINT' ? 3 : 4, // IDs des rôles Point Focal et Poseur Macaron
                    activatedAt: new Date(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            })

            // 2. Créer l'agent
            await tx.agents.create({
                data: {
                    publicId: uuidv4(),
                    lastName: agentData.lastName,
                    firstName: agentData.firstName,
                    email: agentData.email,
                    phone: agentData.phone,
                    userId: user.id,
                    countryId: agentData.countryId,
                    stateId: agentData.stateId,
                    status: 'ACTIVE',
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            })
        })

        console.log(`Agent créé pour ${country.name} (${randomState.name})`)
    }
}