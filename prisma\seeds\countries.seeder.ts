import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid';
import { countries } from './data';

export default async function CountriesSeeder(prisma: PrismaClient) {
    //truncate table
    // Disable foreign key checks and constraints before deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;
    await prisma.$executeRaw`TRUNCATE TABLE countries`;
    // Re-enable foreign key checks and constraints after deletion
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    // Create countries
    for (const country of countries) {
        await prisma.countries.create({
            data: {
                publicId: uuidv4(),
                name: country.name,
                iso: country.iso,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        });
    }
}
