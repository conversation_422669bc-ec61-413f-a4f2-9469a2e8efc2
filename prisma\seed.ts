import { PrismaClient } from '@prisma/client';
import RolesSeeder from './seeds/roles.seeder';
import CountriesSeeder from './seeds/countries.seeder';
import StatesSeeder from './seeds/states.seeder';
import CitiesSeeder from './seeds/cities.seeder';
import ComplaintTypesSeeder from './seeds/complaint_types.seeder';
import VehicleTypesSeeder from './seeds/vehicle_types.seeder';
import UsersSeeder from './seeds/users.seeder';
import AdminsSeeder from './seeds/admins.seeder';
import AgentSeeder from './seeds/agents.seeder';

const prisma = new PrismaClient();

async function main() {
    await RolesSeeder(prisma);
    await CountriesSeeder(prisma);
    await StatesSeeder(prisma);
    await CitiesSeeder(prisma);
    await ComplaintTypesSeeder(prisma)
    await VehicleTypesSeeder(prisma);
    await UsersSeeder(prisma);
    await AdminsSeeder(prisma);
    await AgentSeeder(prisma);
}

main()
    .then(async () => {
        console.log("Seeders exécuter avec succès");
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
