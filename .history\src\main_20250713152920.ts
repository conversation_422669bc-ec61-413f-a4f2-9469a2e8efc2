import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.NATS,
      options: {
        servers: [process.env.NATS_URL || 'nats://localhost:4222'],
        queue: '',
        reconnect: true,
        maxReconnectAttempts: -1,
        reconnectTimeWait: 3000,
        pingInterval: 120000,
      },
    },
  );
  await app.listen();
}
bootstrap();